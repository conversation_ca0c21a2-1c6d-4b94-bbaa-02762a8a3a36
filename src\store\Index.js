import { createStore } from 'vuex'
import webSocketService from '../utils/webSocket.js'
import { userLoginApi } from '../api/modules/userLogin.js'

export default createStore({
	state: {
		accessToken: localStorage.getItem('accessToken') || '', // 短令牌
		refreshToken: localStorage.getItem('refreshToken') || '', // 长令牌
		userInfo: JSON.parse(localStorage.getItem('userInfo')) || null // 用户信息
	},
	mutations: {
		SET_TOKEN(state, data) {
			// 初始化 短令牌
			state.accessToken = data.accessToken
			// 初始化 长令牌
			state.refreshToken = data.refreshToken
			// 缓存到本地
			localStorage.setItem('accessToken', state.accessToken)
			// 缓存到本地
			localStorage.setItem('refreshToken', state.refreshToken)
		},
		CLEAR_AUTH(state) {
			// 清空 短令牌
			state.accessToken = ''
			// 清空 长令牌
			state.refreshToken = ''
			// 清空用户信息
			state.userInfo = null
			// 清除本地缓存
			localStorage.removeItem('accessToken')
			// 清除本地缓存
			localStorage.removeItem('refreshToken')
			// 清除本地缓存
			localStorage.removeItem('userInfo')
			
			// 断开WebSocket连接
			webSocketService.disconnect()
		},
		SET_USER_INFO(state, userInfo) {
			// 初始化 用户信息
			state.userInfo = userInfo
			// 缓存到本地
			localStorage.setItem('userInfo', JSON.stringify(state.userInfo))
		}
	},
	actions: {
		async login({commit}, data) {
			try {
				// 调用登录接口
				// const response = await adminApi.login(data)
				const response = await userLoginApi.userLogin(data)
				console.log("响应",response)

				// 获取实际的业务数据
				const result = response.data
				console.log("业务数据",result)

				// 检查响应状态
				if (result.code !== 200) {
					throw new Error(result.message || '登录失败')
				}

				// 设置 状态
				commit('SET_TOKEN', result.data)

				// 添加更多日志，确保token存在
				console.log("登录成功，准备连接WebSocket")
				console.log("accessToken值:", result.data.accessToken)

				// 登录成功后连接WebSocket
				if (result.data.accessToken) {
					webSocketService.connect(result.data.accessToken)
				} else {
					console.error("无法连接WebSocket: accessToken不存在")
				}

				try {
					// 调用查询接口获取用户信息
					console.log("准备获取用户信息",result.data.accessToken)
					const userInfoResponse = await userLoginApi.getUserInfo(result.data.accessToken)
					const userInfo = userInfoResponse.data

					// 检查响应状态
					if (userInfo.code !== 200) {
						console.error('获取用户信息失败:', userInfo.message)
						// 不清空认证信息，仅返回警告
						return Promise.resolve('登录成功，但获取用户信息失败')
					}

					// 设置用户信息
					commit('SET_USER_INFO', userInfo.data)
				} catch (userError) {
					// 记录错误但不清空认证信息
					console.error('获取用户信息出错:', userError)
					return Promise.resolve('登录成功，但获取用户信息失败')
				}
				
				// 抛出 消息
				return Promise.resolve('登录成功')
			} catch (error) {
				// 清空 状态
				commit('CLEAR_AUTH')
				// 抛出 错误
				return Promise.reject(error)
			}
		},
		logout({commit}) {
			// 清空 状态
			commit('CLEAR_AUTH')
		}
	},
	getters: {
		// 定义 认证属性
		isAuthenticated: state => !!state.accessToken
	}
})
