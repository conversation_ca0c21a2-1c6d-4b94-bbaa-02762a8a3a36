<template>
  <div class="login-container">
    <el-form ref="formRef" :model="form" :rules="rules" @keyup.enter="handleLogin">
      <div class="header">
        <h2 class="title">登录</h2>
        <p class="subtitle">欢迎使用{{ global?.system.name }}管理系统</p>
        <el-divider/>
      </div>
      <el-form-item prop="phonenumber" class="form-item">
        <el-input v-model="form.phonenumber" placeholder="请输入手机号" prefix-icon="User" class="input-item"/>
      </el-form-item>
      <el-form-item prop="password" class="form-item">
        <el-input v-model="form.password" type="password" placeholder="请输入密码" prefix-icon="Lock" show-password
                  class="input-item"/>
      </el-form-item>
      <el-form-item prop="captcha" class="form-item">
        <div class="captcha-wrapper">
          <el-input v-model="form.captcha" placeholder="请输入验证码" prefix-icon="Picture"
                    class="captcha-input input-item"/>
          <img :src="captchaUrl" class="captcha-image" @click="refreshCaptcha">
        </div>
      </el-form-item>
      <el-divider class="custom-divider"/>
      <el-button type="primary" class="login-btn" :loading="loading" @click="handleLogin">登录</el-button>
    </el-form>
  </div>
</template>

<script setup>
	import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
	import { useRouter } from 'vue-router'
	import { ElMessage } from 'element-plus'
	import store from '../../store/Index.js'
	import { nextTick } from 'vue'
	
	// 获取 全局变量
	const global = getCurrentInstance()?.appContext.config.globalProperties.$global

	// 表单数据
	const form = reactive({
		username: '',
		phonenumber: '',
		password: '',
		captcha: ''
	})

	// 验证规则
	const rules = reactive({
		phonenumber: [{required: true, message: '手机号不能为空', trigger: 'blur'}],
		password: [{required: true, message: '密码不能为空', trigger: 'blur'}],
		captcha: [{required: true, message: '验证码不能为空', trigger: 'blur'}]
	})

	// 表单引用
	const formRef = ref(null)

	// 验证码URL
	const captchaUrl = ref('')

	// 防止重复提交
	const loading = ref(false)

	// 路由
	const router = useRouter()

	// 刷新验证码
	const refreshCaptcha = () => {
		captchaUrl.value = `https://newadmin.buzhiyushu.cn/admin/generateCaptcha?t=${Date.now()}`
	}

	// 处理登录
	const handleLogin = async () => {
		if (!formRef.value) return

		await formRef.value.validate(async (valid) => {
			if (valid) {
				try {
					// 锁定按钮
					loading.value = true

					// 创建JSON数据对象
					const sendData = {
						phonenumber: form.phonenumber,
						password: form.password,
						code: form.captcha
					}

					// 调试：打印发送的数据
					console.log('表单数据:', sendData)
					console.log('准备调用store.dispatch("login")')

					// 调用接口
					await store.dispatch('login', sendData)
					console.log('store.dispatch("login")调用成功')
					
					// 定义 跳转地址
					const redirect = router.currentRoute.value.query.redirect || '/welcome'
					console.log("跳转地址",redirect)
					nextTick(() => {
						window.location.href = redirect
						// 执行跳转
						router.replace(redirect)
						ElMessage.success('登录成功')
					})
				} catch (error) {
					console.error('登录错误详情:', error)
					refreshCaptcha()
					ElMessage.error(error.message || '登录失败')
				} finally {
					// 释放按钮
					loading.value = false
				}
			} else {
				// 验证失败，刷新验证码
				refreshCaptcha()
			}
		})
	}

	// 组件挂载时刷新验证码
	onMounted(() => {
		refreshCaptcha()
	})
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f0f2f5;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  margin-bottom: 8px;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  color: #666;
  font-size: 14px;
}

:deep(.el-form) {
  width: 400px;
  padding: 40px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.form-item {
  margin-bottom: 24px;
}

.input-item {
  height: 40px;
}

:deep(.el-input__wrapper) {
  height: 100%;
  padding: 0 12px;
}

:deep(.el-input__inner) {
  height: 100%;
}

.captcha-wrapper {
  display: flex;
  gap: 10px;
  height: 40px;
}

.captcha-input {
  flex: 1;
}

.captcha-image {
  width: 120px;
  height: 40px;
  cursor: pointer;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.custom-divider {
  margin: 24px 0;
}

.login-btn {
  width: 100%;
  height: 40px;
  margin-top: 0;
}

/* 调整分割线颜色 */
:deep(.el-divider__text) {
  color: #909399;
  font-size: 12px;
}

:deep(.el-divider--horizontal) {
  margin: 18px 0;
  border-top: 1px solid #e8e8e8;
}
</style>