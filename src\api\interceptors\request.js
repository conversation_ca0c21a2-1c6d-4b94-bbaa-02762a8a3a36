//配置拦截器
export function setupRequestInterceptors(instance) {
	// 这个拦截器会在每个请求被发送之前执行
	instance.interceptors.request.use(config => {
		// 登录相关接口不需要添加Authorization头部
		const loginPaths = ['/userLogin/login', '/admin/login', '/admin/refreshToken']
		const isLoginRequest = loginPaths.some(path => config.url.includes(path))

		if (!isLoginRequest) {
			// 从浏览器的 localStorage 中获取名为 'accessToken' 的 token 字符串
			const token = localStorage.getItem('accessToken')
			 // 判断 token 是否存在
			if (token) {
				// 如果存在，则将 token 添加到请求头中的 Authorization 字段（不添加Bearer前缀）
				config.headers.Authorization = token
			}
		}

		// 返回修改后的请求配置，继续发送请求
		return config
	}, error => {
		console.log("请求错误",error)
		// 将错误以 Promise reject 的方式抛出，供调用方捕获处理
		return Promise.reject(error)
	})
}